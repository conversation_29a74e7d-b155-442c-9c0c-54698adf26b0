import streamlit as st
import pandas as pd
import numpy as np
import joblib

# Streamlit UI
st.title("Heart Disease Prediction")
st.write("Enter the patient information below to predict heart disease status.")
model = joblib.load('LR.pkl')
scaler = joblib.load('scaler.pkl')
expected_coloumns = joblib.load('coloums.pkl')


# Input fields
age = st.slider("Age", 18, 100, 50)
resting_bp = st.slider("Resting Blood Pressure", 50, 200, 120)
cholesterol = st.slider("Cholesterol", 100, 600, 200)
fasting_bs = st.selectbox("Fasting Blood Sugar > 120 mg/dl", [0, 1])
max_hr = st.slider("Maximum Heart Rate", 60, 220, 150)
oldpeak = st.number_input("Oldpeak (ST depression)", value=1.0)

# Categorical Inputs converted to One-Hot format
sex = st.radio("Sex", ["Male", "Female"])
chest_pain = st.selectbox("Chest Pain Type", ["ASY", "ATA", "NAP", "TA"])
resting_ecg = st.selectbox("Resting ECG", ["Normal", "ST"])
exercise_angina = st.radio("Exercise Angina", ["Y", "N"])
st_slope = st.selectbox("ST Slope", ["Down", "Flat", "Up"])

# Convert inputs to feature format
input_dict = {
    'Age': age,
    'RestingBP': resting_bp,
    'Cholesterol': cholesterol,
    'FastingBS': fasting_bs,
    'MaxHR': max_hr,
    'Oldpeak': oldpeak,
    'Sex_F': 1 if sex == "Female" else 0,
    'Sex_M': 1 if sex == "Male" else 0,
    'ChestPainType_ASY': 1 if chest_pain == "ASY" else 0,
    'ChestPainType_ATA': 1 if chest_pain == "ATA" else 0,
    'ChestPainType_NAP': 1 if chest_pain == "NAP" else 0,
    'ChestPainType_TA': 1 if chest_pain == "TA" else 0,
    'RestingECG_Normal': 1 if resting_ecg == "Normal" else 0,
    'RestingECG_ST': 1 if resting_ecg == "ST" else 0,
    'ExerciseAngina_N': 1 if exercise_angina == "N" else 0,
    'ExerciseAngina_Y': 1 if exercise_angina == "Y" else 0,
    'ST_Slope_Down': 1 if st_slope == "Down" else 0,
    'ST_Slope_Flat': 1 if st_slope == "Flat" else 0,
    'ST_Slope_Up': 1 if st_slope == "Up" else 0,
    'disease_bin': 0  # Placeholder for target variable
}

# Convert to DataFrame
input_df = pd.DataFrame([input_dict])

# Ensure all expected columns are present and in correct order
for col in expected_coloumns:
    if col not in input_df.columns:
        input_df[col] = 0  # Fill missing columns with 0

# Reorder columns to match expected order
input_df = input_df[expected_coloumns]

# Handle missing values using a loop (fill with median/mode values)
for col in input_df.columns:
    if input_df[col].isnull().any():
        if input_df[col].dtype in ['int64', 'float64']:
            # Fill numerical columns with median
            input_df[col].fillna(input_df[col].median(), inplace=True)
        else:
            # Fill categorical columns with mode
            input_df[col].fillna(input_df[col].mode()[0], inplace=True)

# Remove target variable before prediction (if present)
if 'disease_bin' in input_df.columns:
    input_df_for_prediction = input_df.drop('disease_bin', axis=1)
else:
    input_df_for_prediction = input_df

# Separate numerical and categorical features
numerical_features = ['Age', 'RestingBP', 'Cholesterol', 'MaxHR']
categorical_features = [col for col in input_df_for_prediction.columns if col not in numerical_features]

# Scale only numerical features
numerical_data = input_df_for_prediction[numerical_features]
scaled_numerical = scaler.transform(numerical_data)
scaled_numerical_df = pd.DataFrame(scaled_numerical, columns=numerical_features)

# Combine scaled numerical features with categorical features
categorical_data = input_df_for_prediction[categorical_features]
final_input = pd.concat([scaled_numerical_df, categorical_data.reset_index(drop=True)], axis=1)

# Ensure columns are in the same order as model expects (excluding disease_bin)
model_features = [col for col in model.feature_names_in_ if col != 'disease_bin']
final_input = final_input[model_features]

prediction = model.predict(final_input)[0]

# Predict
if prediction == 1:
    st.error("The patient is predicted to have heart disease.")
else:
    st.success("The patient is predicted to not have heart disease.")